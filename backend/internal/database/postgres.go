package database

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"

	"backend/internal/config"
)

// PostgresDB wraps the database connection (either pooled or direct)
type PostgresDB struct {
	Pool         *pgxpool.Pool // Used when UsePooler is true
	Conn         *pgx.Conn     // Used when UsePooler is false
	QueryTimeout time.Duration
	UsePooler    bool
}

// NewPostgresDB creates a new PostgreSQL database connection
func NewPostgresDB(cfg *config.DatabaseConfig) (*PostgresDB, error) {
	// Create connection string
	connStr := cfg.ConnectionString()

	if cfg.UsePooler {
		return newPooledConnection(cfg, connStr)
	}
	return newDirectConnection(cfg, connStr)
}

// newPooledConnection creates a pooled database connection
func newPooledConnection(cfg *config.DatabaseConfig, connStr string) (*PostgresDB, error) {
	// Configure connection pool
	poolConfig, err := pgxpool.ParseConfig(connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to parse database config: %w", err)
	}

	// Set pool configuration
	poolConfig.MaxConns = int32(cfg.MaxConns)
	poolConfig.MinConns = int32(cfg.MinConns)
	poolConfig.MaxConnLifetime = time.Hour
	poolConfig.MaxConnIdleTime = time.Minute * 30

	// Set connection timeout
	poolConfig.ConnConfig.ConnectTimeout = cfg.ConnectTimeout

	// Create connection pool with timeout context
	ctx, cancel := context.WithTimeout(context.Background(), cfg.ConnectTimeout)
	defer cancel()

	pool, err := pgxpool.NewWithConfig(ctx, poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection pool: %w", err)
	}

	// Test the connection with timeout
	pingCtx, pingCancel := context.WithTimeout(context.Background(), cfg.ConnectTimeout)
	defer pingCancel()

	if err := pool.Ping(pingCtx); err != nil {
		pool.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return &PostgresDB{
		Pool:         pool,
		Conn:         nil,
		QueryTimeout: cfg.QueryTimeout,
		UsePooler:    true,
	}, nil
}

// newDirectConnection creates a direct database connection
func newDirectConnection(cfg *config.DatabaseConfig, connStr string) (*PostgresDB, error) {
	// Parse connection config
	connConfig, err := pgx.ParseConfig(connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to parse database config: %w", err)
	}

	// Set connection timeout
	connConfig.ConnectTimeout = cfg.ConnectTimeout

	// Create direct connection with timeout context
	ctx, cancel := context.WithTimeout(context.Background(), cfg.ConnectTimeout)
	defer cancel()

	conn, err := pgx.ConnectConfig(ctx, connConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create direct connection: %w", err)
	}

	// Test the connection with timeout
	pingCtx, pingCancel := context.WithTimeout(context.Background(), cfg.ConnectTimeout)
	defer pingCancel()

	if err := conn.Ping(pingCtx); err != nil {
		conn.Close(context.Background())
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return &PostgresDB{
		Pool:         nil,
		Conn:         conn,
		QueryTimeout: cfg.QueryTimeout,
		UsePooler:    false,
	}, nil
}

// Close closes the database connection (pool or direct)
func (db *PostgresDB) Close() {
	if db.UsePooler && db.Pool != nil {
		db.Pool.Close()
	} else if !db.UsePooler && db.Conn != nil {
		db.Conn.Close(context.Background())
	}
}

// HealthCheck performs a health check on the database
func (db *PostgresDB) HealthCheck(ctx context.Context) error {
	if db.UsePooler {
		if db.Pool == nil {
			return fmt.Errorf("database connection pool is nil")
		}
		return db.healthCheckPool(ctx)
	} else {
		if db.Conn == nil {
			return fmt.Errorf("database connection is nil")
		}
		return db.healthCheckDirect(ctx)
	}
}

// healthCheckPool performs health check using connection pool
func (db *PostgresDB) healthCheckPool(ctx context.Context) error {
	// Create timeout context for the health check query
	queryCtx, cancel := context.WithTimeout(ctx, db.QueryTimeout)
	defer cancel()

	// Test with a simple query
	var result int
	err := db.Pool.QueryRow(queryCtx, "SELECT 1").Scan(&result)
	if err != nil {
		return fmt.Errorf("database health check failed: %w", err)
	}

	if result != 1 {
		return fmt.Errorf("unexpected health check result: %d", result)
	}

	return nil
}

// healthCheckDirect performs health check using direct connection
func (db *PostgresDB) healthCheckDirect(ctx context.Context) error {
	// Create timeout context for the health check query
	queryCtx, cancel := context.WithTimeout(ctx, db.QueryTimeout)
	defer cancel()

	// Test with a simple query
	var result int
	err := db.Conn.QueryRow(queryCtx, "SELECT 1").Scan(&result)
	if err != nil {
		return fmt.Errorf("database health check failed: %w", err)
	}

	if result != 1 {
		return fmt.Errorf("unexpected health check result: %d", result)
	}

	return nil
}

// DBInterface defines the common interface for both pooled and direct connections
type DBInterface interface {
	Exec(ctx context.Context, sql string, arguments ...interface{}) (pgconn.CommandTag, error)
	Query(ctx context.Context, sql string, args ...interface{}) (pgx.Rows, error)
	QueryRow(ctx context.Context, sql string, args ...interface{}) pgx.Row
	Begin(ctx context.Context) (pgx.Tx, error)
}

// GetDB returns the appropriate database interface for queries
// This provides a unified interface for both pooled and direct connections
func (db *PostgresDB) GetDB() DBInterface {
	if db.UsePooler {
		return db.Pool
	}
	return db.Conn
}
