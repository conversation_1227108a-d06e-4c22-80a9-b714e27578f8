package database

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"

	"backend/internal/config"
)

// PostgresDB wraps the database connection pool
type PostgresDB struct {
	Pool         *pgxpool.Pool
	QueryTimeout time.Duration
}

// NewPostgresDB creates a new PostgreSQL database connection
func NewPostgresDB(cfg *config.DatabaseConfig) (*PostgresDB, error) {
	// Create connection string
	connStr := cfg.ConnectionString()

	// Configure connection pool
	poolConfig, err := pgxpool.ParseConfig(connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to parse database config: %w", err)
	}

	// Set pool configuration
	poolConfig.MaxConns = int32(cfg.MaxConns)
	poolConfig.MinConns = int32(cfg.MinConns)
	poolConfig.MaxConnLifetime = time.Hour
	poolConfig.MaxConnIdleTime = time.Minute * 30

	// Set connection timeout
	poolConfig.ConnConfig.ConnectTimeout = cfg.ConnectTimeout

	// Create connection pool with timeout context
	ctx, cancel := context.WithTimeout(context.Background(), cfg.ConnectTimeout)
	defer cancel()

	pool, err := pgxpool.NewWithConfig(ctx, poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection pool: %w", err)
	}

	// Test the connection with timeout
	pingCtx, pingCancel := context.WithTimeout(context.Background(), cfg.ConnectTimeout)
	defer pingCancel()

	if err := pool.Ping(pingCtx); err != nil {
		pool.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return &PostgresDB{
		Pool:         pool,
		QueryTimeout: cfg.QueryTimeout,
	}, nil
}

// Close closes the database connection pool
func (db *PostgresDB) Close() {
	if db.Pool != nil {
		db.Pool.Close()
	}
}

// HealthCheck performs a health check on the database
func (db *PostgresDB) HealthCheck(ctx context.Context) error {
	if db.Pool == nil {
		return fmt.Errorf("database connection pool is nil")
	}

	// Create timeout context for the health check query
	queryCtx, cancel := context.WithTimeout(ctx, db.QueryTimeout)
	defer cancel()

	// Test with a simple query
	var result int
	err := db.Pool.QueryRow(queryCtx, "SELECT 1").Scan(&result)
	if err != nil {
		return fmt.Errorf("database health check failed: %w", err)
	}

	if result != 1 {
		return fmt.Errorf("unexpected health check result: %d", result)
	}

	return nil
}
