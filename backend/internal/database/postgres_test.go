package database

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"backend/internal/config"
)

func TestNewPostgresDB_WithPooler(t *testing.T) {
	// Skip if no database connection available
	if os.Getenv("DB_HOST") == "" {
		t.Skip("Skipping database test: DB_HOST not set")
	}

	cfg := &config.DatabaseConfig{
		Host:           getEnvOrDefault("DB_HOST", "localhost"),
		Port:           5432,
		Name:           getEnvOrDefault("DB_NAME", "todo_test"),
		User:           getEnvOrDefault("DB_USER", "postgres"),
		Password:       getEnvOrDefault("DB_PASSWORD", ""),
		SSLMode:        "prefer",
		MaxConns:       5,
		MinConns:       1,
		ConnectTimeout: 5 * time.Second,
		QueryTimeout:   10 * time.Second,
		UsePooler:      true, // Force pooler usage
	}

	db, err := NewPostgresDB(cfg)
	require.NoError(t, err, "Failed to create pooled database connection")
	defer db.Close()

	// Verify pooler is being used
	assert.True(t, db.UsePooler, "Expected UsePooler to be true")
	assert.NotNil(t, db.Pool, "Expected Pool to be non-nil when using pooler")
	assert.Nil(t, db.Conn, "Expected Conn to be nil when using pooler")

	// Test health check
	ctx := context.Background()
	err = db.HealthCheck(ctx)
	assert.NoError(t, err, "Health check should pass for pooled connection")

	// Test database interface
	dbInterface := db.GetDB()
	assert.NotNil(t, dbInterface, "GetDB should return non-nil interface")

	// Test a simple query
	var result int
	err = dbInterface.QueryRow(ctx, "SELECT 1").Scan(&result)
	assert.NoError(t, err, "Simple query should work")
	assert.Equal(t, 1, result, "Query result should be 1")
}

func TestNewPostgresDB_WithoutPooler(t *testing.T) {
	// Skip if no database connection available
	if os.Getenv("DB_HOST") == "" {
		t.Skip("Skipping database test: DB_HOST not set")
	}

	cfg := &config.DatabaseConfig{
		Host:           getEnvOrDefault("DB_HOST", "localhost"),
		Port:           5432,
		Name:           getEnvOrDefault("DB_NAME", "todo_test"),
		User:           getEnvOrDefault("DB_USER", "postgres"),
		Password:       getEnvOrDefault("DB_PASSWORD", ""),
		SSLMode:        "prefer",
		MaxConns:       5,
		MinConns:       1,
		ConnectTimeout: 5 * time.Second,
		QueryTimeout:   10 * time.Second,
		UsePooler:      false, // Force direct connection
	}

	db, err := NewPostgresDB(cfg)
	require.NoError(t, err, "Failed to create direct database connection")
	defer db.Close()

	// Verify direct connection is being used
	assert.False(t, db.UsePooler, "Expected UsePooler to be false")
	assert.Nil(t, db.Pool, "Expected Pool to be nil when using direct connection")
	assert.NotNil(t, db.Conn, "Expected Conn to be non-nil when using direct connection")

	// Test health check
	ctx := context.Background()
	err = db.HealthCheck(ctx)
	assert.NoError(t, err, "Health check should pass for direct connection")

	// Test database interface
	dbInterface := db.GetDB()
	assert.NotNil(t, dbInterface, "GetDB should return non-nil interface")

	// Test a simple query
	var result int
	err = dbInterface.QueryRow(ctx, "SELECT 1").Scan(&result)
	assert.NoError(t, err, "Simple query should work")
	assert.Equal(t, 1, result, "Query result should be 1")
}

func TestNewPostgresDB_InvalidConfig(t *testing.T) {
	cfg := &config.DatabaseConfig{
		Host:           "invalid-host-that-does-not-exist",
		Port:           5432,
		Name:           "invalid_db",
		User:           "invalid_user",
		Password:       "invalid_password",
		SSLMode:        "prefer",
		MaxConns:       5,
		MinConns:       1,
		ConnectTimeout: 1 * time.Second, // Short timeout for quick failure
		QueryTimeout:   5 * time.Second,
		UsePooler:      true,
	}

	_, err := NewPostgresDB(cfg)
	assert.Error(t, err, "Should fail with invalid configuration")
}

// getEnvOrDefault gets an environment variable or returns a default value
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
