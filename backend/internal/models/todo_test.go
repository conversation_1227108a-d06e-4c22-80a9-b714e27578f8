package models

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestNewTodo(t *testing.T) {
	title := "Test Todo"
	description := "Test Description"

	todo := NewTodo(title, description)

	assert.NotEmpty(t, todo.ID)
	assert.Equal(t, title, todo.Title)
	assert.Equal(t, description, todo.Description)
	assert.False(t, todo.Completed)
	assert.WithinDuration(t, time.Now(), todo.CreatedAt, time.Second)
	assert.WithinDuration(t, time.Now(), todo.UpdatedAt, time.Second)
	assert.Equal(t, todo.CreatedAt, todo.UpdatedAt)
}

func TestTodo_Update(t *testing.T) {
	todo := NewTodo("Original Title", "Original Description")
	originalCreatedAt := todo.CreatedAt
	originalUpdatedAt := todo.UpdatedAt

	// Wait a bit to ensure UpdatedAt changes
	time.Sleep(time.Millisecond * 10)

	newTitle := "Updated Title"
	newDescription := "Updated Description"
	completed := true

	updateReq := &UpdateTodoRequest{
		Title:       &newTitle,
		Description: &newDescription,
		Completed:   &completed,
	}

	todo.Update(updateReq)

	assert.Equal(t, newTitle, todo.Title)
	assert.Equal(t, newDescription, todo.Description)
	assert.True(t, todo.Completed)
	assert.Equal(t, originalCreatedAt, todo.CreatedAt)      // CreatedAt should not change
	assert.True(t, todo.UpdatedAt.After(originalUpdatedAt)) // UpdatedAt should change
}

func TestTodo_UpdatePartial(t *testing.T) {
	todo := NewTodo("Original Title", "Original Description")
	originalTitle := todo.Title
	originalDescription := todo.Description

	// Update only the completed status
	completed := true
	updateReq := &UpdateTodoRequest{
		Completed: &completed,
	}

	todo.Update(updateReq)

	assert.Equal(t, originalTitle, todo.Title)             // Should remain unchanged
	assert.Equal(t, originalDescription, todo.Description) // Should remain unchanged
	assert.True(t, todo.Completed)                         // Should be updated
}

func TestTodo_UpdateWithNilValues(t *testing.T) {
	todo := NewTodo("Original Title", "Original Description")
	originalTitle := todo.Title
	originalDescription := todo.Description
	originalCompleted := todo.Completed

	// Update with all nil values
	updateReq := &UpdateTodoRequest{}

	todo.Update(updateReq)

	assert.Equal(t, originalTitle, todo.Title)
	assert.Equal(t, originalDescription, todo.Description)
	assert.Equal(t, originalCompleted, todo.Completed)
}
