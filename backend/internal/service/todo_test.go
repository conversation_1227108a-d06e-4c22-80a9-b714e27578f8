package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"backend/internal/models"
	"backend/test/testutil"
)

func TestTodoService_CreateTodo(t *testing.T) {
	repo, cleanup := testutil.SetupTestDB(t)
	defer cleanup()
	service := NewTodoService(repo)
	ctx := context.Background()

	req := &models.CreateTodoRequest{
		Title:       "Test Todo",
		Description: "Test Description",
	}

	todo, err := service.CreateTodo(ctx, req)
	require.NoError(t, err)
	assert.NotEmpty(t, todo.ID)
	assert.Equal(t, req.Title, todo.Title)
	assert.Equal(t, req.Description, todo.Description)
	assert.False(t, todo.Completed)
}

func TestTodoService_CreateTodoValidation(t *testing.T) {
	repo, cleanup := testutil.SetupTestDB(t)
	defer cleanup()
	service := NewTodoService(repo)
	ctx := context.Background()

	tests := []struct {
		name    string
		request *models.CreateTodoRequest
		wantErr bool
	}{
		{
			name: "valid request",
			request: &models.CreateTodoRequest{
				Title:       "Valid Title",
				Description: "Valid Description",
			},
			wantErr: false,
		},
		{
			name: "empty title",
			request: &models.CreateTodoRequest{
				Title:       "",
				Description: "Valid Description",
			},
			wantErr: true,
		},
		{
			name: "title too long",
			request: &models.CreateTodoRequest{
				Title:       string(make([]byte, 201)), // 201 characters
				Description: "Valid Description",
			},
			wantErr: true,
		},
		{
			name: "description too long",
			request: &models.CreateTodoRequest{
				Title:       "Valid Title",
				Description: string(make([]byte, 1001)), // 1001 characters
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := service.CreateTodo(ctx, tt.request)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestTodoService_CreateTodoTrimWhitespace(t *testing.T) {
	repo, cleanup := testutil.SetupTestDB(t)
	defer cleanup()
	service := NewTodoService(repo)
	ctx := context.Background()

	req := &models.CreateTodoRequest{
		Title:       "  Test Todo  ",
		Description: "  Test Description  ",
	}

	todo, err := service.CreateTodo(ctx, req)
	require.NoError(t, err)
	assert.Equal(t, "Test Todo", todo.Title)
	assert.Equal(t, "Test Description", todo.Description)
}

func TestTodoService_GetTodoByID(t *testing.T) {
	repo, cleanup := testutil.SetupTestDB(t)
	defer cleanup()
	service := NewTodoService(repo)
	ctx := context.Background()

	// Create a todo first
	createReq := &models.CreateTodoRequest{
		Title:       "Test Todo",
		Description: "Test Description",
	}
	created, err := service.CreateTodo(ctx, createReq)
	require.NoError(t, err)

	// Get the todo
	retrieved, err := service.GetTodoByID(ctx, created.ID)
	require.NoError(t, err)
	assert.Equal(t, created.ID, retrieved.ID)
	assert.Equal(t, created.Title, retrieved.Title)
}

func TestTodoService_GetTodoByIDInvalidID(t *testing.T) {
	repo, cleanup := testutil.SetupTestDB(t)
	defer cleanup()
	service := NewTodoService(repo)
	ctx := context.Background()

	_, err := service.GetTodoByID(ctx, "invalid-uuid")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid todo ID")
}

func TestTodoService_UpdateTodo(t *testing.T) {
	repo, cleanup := testutil.SetupTestDB(t)
	defer cleanup()
	service := NewTodoService(repo)
	ctx := context.Background()

	// Create a todo first
	createReq := &models.CreateTodoRequest{
		Title:       "Original Title",
		Description: "Original Description",
	}
	created, err := service.CreateTodo(ctx, createReq)
	require.NoError(t, err)

	// Update the todo
	newTitle := "Updated Title"
	completed := true
	updateReq := &models.UpdateTodoRequest{
		Title:     &newTitle,
		Completed: &completed,
	}

	updated, err := service.UpdateTodo(ctx, created.ID, updateReq)
	require.NoError(t, err)
	assert.Equal(t, newTitle, updated.Title)
	assert.Equal(t, created.Description, updated.Description) // Should remain unchanged
	assert.True(t, updated.Completed)
}

func TestTodoService_DeleteTodo(t *testing.T) {
	repo, cleanup := testutil.SetupTestDB(t)
	defer cleanup()
	service := NewTodoService(repo)
	ctx := context.Background()

	// Create a todo first
	createReq := &models.CreateTodoRequest{
		Title:       "Test Todo",
		Description: "Test Description",
	}
	created, err := service.CreateTodo(ctx, createReq)
	require.NoError(t, err)

	// Delete the todo
	err = service.DeleteTodo(ctx, created.ID)
	require.NoError(t, err)

	// Verify deletion
	_, err = service.GetTodoByID(ctx, created.ID)
	assert.Error(t, err)
}

func TestTodoService_BatchDeleteTodos(t *testing.T) {
	repo, cleanup := testutil.SetupTestDB(t)
	defer cleanup()
	service := NewTodoService(repo)
	ctx := context.Background()

	// Create multiple todos
	createReq1 := &models.CreateTodoRequest{
		Title:       "Test Todo 1",
		Description: "Test Description 1",
	}
	created1, err := service.CreateTodo(ctx, createReq1)
	require.NoError(t, err)

	createReq2 := &models.CreateTodoRequest{
		Title:       "Test Todo 2",
		Description: "Test Description 2",
	}
	created2, err := service.CreateTodo(ctx, createReq2)
	require.NoError(t, err)

	createReq3 := &models.CreateTodoRequest{
		Title:       "Test Todo 3",
		Description: "Test Description 3",
	}
	created3, err := service.CreateTodo(ctx, createReq3)
	require.NoError(t, err)

	// Batch delete two todos
	batchReq := &models.BatchDeleteTodosRequest{
		IDs: []string{created1.ID, created2.ID},
	}
	deletedCount, err := service.BatchDeleteTodos(ctx, batchReq)
	require.NoError(t, err)
	assert.Equal(t, int64(2), deletedCount)

	// Verify deletions
	_, err = service.GetTodoByID(ctx, created1.ID)
	assert.Error(t, err)
	_, err = service.GetTodoByID(ctx, created2.ID)
	assert.Error(t, err)

	// Verify third todo still exists
	retrievedTodo, err := service.GetTodoByID(ctx, created3.ID)
	require.NoError(t, err)
	assert.Equal(t, created3.ID, retrievedTodo.ID)
}

func TestTodoService_BatchDeleteTodos_InvalidIDs(t *testing.T) {
	repo, cleanup := testutil.SetupTestDB(t)
	defer cleanup()
	service := NewTodoService(repo)
	ctx := context.Background()

	// Test with invalid UUID format
	batchReq := &models.BatchDeleteTodosRequest{
		IDs: []string{"invalid-uuid", "another-invalid"},
	}
	_, err := service.BatchDeleteTodos(ctx, batchReq)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid todo ID format")
}

func TestTodoService_BatchDeleteTodos_EmptyRequest(t *testing.T) {
	repo, cleanup := testutil.SetupTestDB(t)
	defer cleanup()
	service := NewTodoService(repo)
	ctx := context.Background()

	// Test with empty IDs list
	batchReq := &models.BatchDeleteTodosRequest{
		IDs: []string{},
	}
	_, err := service.BatchDeleteTodos(ctx, batchReq)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "validation failed")
}

func TestTodoService_BatchDeleteTodos_WithDuplicatesAndWhitespace(t *testing.T) {
	repo, cleanup := testutil.SetupTestDB(t)
	defer cleanup()
	service := NewTodoService(repo)
	ctx := context.Background()

	// Create a todo
	createReq := &models.CreateTodoRequest{
		Title:       "Test Todo",
		Description: "Test Description",
	}
	created, err := service.CreateTodo(ctx, createReq)
	require.NoError(t, err)

	// Batch delete with duplicates and whitespace
	batchReq := &models.BatchDeleteTodosRequest{
		IDs: []string{
			created.ID,
			" " + created.ID + " ", // Same ID with whitespace
			created.ID,             // Duplicate
			"",                     // Empty string
		},
	}
	deletedCount, err := service.BatchDeleteTodos(ctx, batchReq)
	require.NoError(t, err)
	assert.Equal(t, int64(1), deletedCount) // Only one unique todo deleted

	// Verify deletion
	_, err = service.GetTodoByID(ctx, created.ID)
	assert.Error(t, err)
}
