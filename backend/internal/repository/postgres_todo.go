package repository

import (
	"context"
	"errors"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"

	"backend/internal/database"
	apperrors "backend/internal/errors"
	"backend/internal/models"
)

// PostgresTodoRepository implements TodoRepository using PostgreSQL
type PostgresTodoRepository struct {
	db database.DBInterface
}

// NewPostgresTodoRepository creates a new PostgreSQL todo repository
func NewPostgresTodoRepository(db database.DBInterface) *PostgresTodoRepository {
	return &PostgresTodoRepository{db: db}
}

// Create adds a new todo to the database
func (r *PostgresTodoRepository) Create(ctx context.Context, todo *models.Todo) error {
	query := `
		INSERT INTO todos (id, title, description, completed, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6)
	`

	_, err := r.db.Exec(ctx, query,
		todo.ID,
		todo.Title,
		todo.Description,
		todo.Completed,
		todo.CreatedAt,
		todo.UpdatedAt,
	)

	if err != nil {
		// Check for unique constraint violation
		if isUniqueViolation(err) {
			return apperrors.NewBadRequestError("todo with this ID already exists")
		}
		return apperrors.NewInternalServerError("failed to create todo", err)
	}

	return nil
}

// GetByID retrieves a todo by its ID
func (r *PostgresTodoRepository) GetByID(ctx context.Context, id string) (*models.Todo, error) {
	query := `
		SELECT id, title, description, completed, created_at, updated_at
		FROM todos
		WHERE id = $1
	`

	var todo models.Todo
	err := r.db.QueryRow(ctx, query, id).Scan(
		&todo.ID,
		&todo.Title,
		&todo.Description,
		&todo.Completed,
		&todo.CreatedAt,
		&todo.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, apperrors.ErrTodoNotFound
		}
		return nil, apperrors.NewInternalServerError("failed to get todo", err)
	}

	return &todo, nil
}

// GetAll retrieves all todos
func (r *PostgresTodoRepository) GetAll(ctx context.Context) ([]*models.Todo, error) {
	query := `
		SELECT id, title, description, completed, created_at, updated_at
		FROM todos
		ORDER BY created_at DESC
	`

	rows, err := r.db.Query(ctx, query)
	if err != nil {
		return nil, apperrors.NewInternalServerError("failed to get todos", err)
	}
	defer rows.Close()

	var todos []*models.Todo
	for rows.Next() {
		var todo models.Todo
		err := rows.Scan(
			&todo.ID,
			&todo.Title,
			&todo.Description,
			&todo.Completed,
			&todo.CreatedAt,
			&todo.UpdatedAt,
		)
		if err != nil {
			return nil, apperrors.NewInternalServerError("failed to scan todo", err)
		}
		todos = append(todos, &todo)
	}

	if err := rows.Err(); err != nil {
		return nil, apperrors.NewInternalServerError("error iterating todos", err)
	}

	return todos, nil
}

// Update modifies an existing todo
func (r *PostgresTodoRepository) Update(ctx context.Context, todo *models.Todo) error {
	query := `
		UPDATE todos
		SET title = $2, description = $3, completed = $4, updated_at = $5
		WHERE id = $1
	`

	result, err := r.db.Exec(ctx, query,
		todo.ID,
		todo.Title,
		todo.Description,
		todo.Completed,
		todo.UpdatedAt,
	)

	if err != nil {
		return apperrors.NewInternalServerError("failed to update todo", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return apperrors.ErrTodoNotFound
	}

	return nil
}

// Delete removes a todo by its ID
func (r *PostgresTodoRepository) Delete(ctx context.Context, id string) error {
	query := `DELETE FROM todos WHERE id = $1`

	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return apperrors.NewInternalServerError("failed to delete todo", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return apperrors.ErrTodoNotFound
	}

	return nil
}

// BatchDelete removes multiple todos by their IDs atomically
func (r *PostgresTodoRepository) BatchDelete(ctx context.Context, ids []string) (int64, error) {
	if len(ids) == 0 {
		return 0, apperrors.NewBadRequestError("no todo IDs provided")
	}

	// Start a transaction for atomic operation
	tx, err := r.db.Begin(ctx)
	if err != nil {
		return 0, apperrors.NewInternalServerError("failed to start transaction", err)
	}
	defer tx.Rollback(ctx) // This will be a no-op if the transaction is committed

	// Use ANY() with array parameter for efficient batch deletion
	query := `DELETE FROM todos WHERE id = ANY($1)`

	result, err := tx.Exec(ctx, query, ids)
	if err != nil {
		return 0, apperrors.NewInternalServerError("failed to batch delete todos", err)
	}

	rowsAffected := result.RowsAffected()

	// Commit the transaction
	if err := tx.Commit(ctx); err != nil {
		return 0, apperrors.NewInternalServerError("failed to commit batch delete transaction", err)
	}

	return rowsAffected, nil
}

// isUniqueViolation checks if the error is a unique constraint violation
func isUniqueViolation(err error) bool {
	if err == nil {
		return false
	}

	// Check for PostgreSQL unique violation error code (23505)
	var pgErr *pgconn.PgError
	if errors.As(err, &pgErr) {
		return pgErr.Code == "23505"
	}

	return false
}
