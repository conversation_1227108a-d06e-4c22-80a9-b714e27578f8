package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"backend/internal/models"
	"backend/internal/service"
	"backend/test/testutil"
)

func setupTestHandler(t *testing.T) (*TodoHandler, *echo.Echo, func()) {
	repo, cleanup := testutil.SetupTestDB(t)
	svc := service.NewTodoService(repo)
	handler := NewTodoHandler(svc)
	e := echo.New()
	return handler, e, cleanup
}

func TestTodoHandler_CreateTodo(t *testing.T) {
	handler, e, cleanup := setupTestHandler(t)
	defer cleanup()

	reqBody := models.CreateTodoRequest{
		Title:       "Test Todo",
		Description: "Test Description",
	}
	body, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPost, "/todos", bytes.NewReader(body))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	err := handler.CreateTodo(c)
	require.NoError(t, err)

	assert.Equal(t, http.StatusCreated, rec.Code)

	var response models.Todo
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, reqBody.Title, response.Title)
	assert.Equal(t, reqBody.Description, response.Description)
	assert.NotEmpty(t, response.ID)
}

func TestTodoHandler_CreateTodoInvalidJSON(t *testing.T) {
	handler, e, cleanup := setupTestHandler(t)
	defer cleanup()

	req := httptest.NewRequest(http.MethodPost, "/todos", bytes.NewReader([]byte("invalid json")))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	err := handler.CreateTodo(c)
	require.NoError(t, err)

	assert.Equal(t, http.StatusBadRequest, rec.Code)
}

func TestTodoHandler_GetTodo(t *testing.T) {
	handler, e, cleanup := setupTestHandler(t)
	defer cleanup()

	// Create a todo first
	reqBody := models.CreateTodoRequest{
		Title:       "Test Todo",
		Description: "Test Description",
	}
	body, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPost, "/todos", bytes.NewReader(body))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	err := handler.CreateTodo(c)
	require.NoError(t, err)

	var created models.Todo
	err = json.Unmarshal(rec.Body.Bytes(), &created)
	require.NoError(t, err)

	// Now get the todo
	req = httptest.NewRequest(http.MethodGet, "/todos/"+created.ID, nil)
	rec = httptest.NewRecorder()
	c = e.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(created.ID)

	err = handler.GetTodo(c)
	require.NoError(t, err)

	assert.Equal(t, http.StatusOK, rec.Code)

	var response models.Todo
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, created.ID, response.ID)
	assert.Equal(t, created.Title, response.Title)
}

func TestTodoHandler_GetTodoNotFound(t *testing.T) {
	handler, e, cleanup := setupTestHandler(t)
	defer cleanup()

	req := httptest.NewRequest(http.MethodGet, "/todos/550e8400-e29b-41d4-a716-446655440000", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues("550e8400-e29b-41d4-a716-446655440000")

	err := handler.GetTodo(c)
	require.NoError(t, err)

	assert.Equal(t, http.StatusNotFound, rec.Code)
}

func TestTodoHandler_GetAllTodos(t *testing.T) {
	handler, e, cleanup := setupTestHandler(t)
	defer cleanup()

	req := httptest.NewRequest(http.MethodGet, "/todos", nil)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	err := handler.GetAllTodos(c)
	require.NoError(t, err)

	assert.Equal(t, http.StatusOK, rec.Code)

	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Contains(t, response, "todos")
	assert.Contains(t, response, "count")
	assert.Equal(t, float64(0), response["count"]) // Initially empty
}

func TestTodoHandler_UpdateTodo(t *testing.T) {
	handler, e, cleanup := setupTestHandler(t)
	defer cleanup()

	// Create a todo first
	reqBody := models.CreateTodoRequest{
		Title:       "Original Title",
		Description: "Original Description",
	}
	body, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPost, "/todos", bytes.NewReader(body))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	err := handler.CreateTodo(c)
	require.NoError(t, err)

	var created models.Todo
	err = json.Unmarshal(rec.Body.Bytes(), &created)
	require.NoError(t, err)

	// Update the todo
	newTitle := "Updated Title"
	completed := true
	updateReq := models.UpdateTodoRequest{
		Title:     &newTitle,
		Completed: &completed,
	}
	body, _ = json.Marshal(updateReq)

	req = httptest.NewRequest(http.MethodPut, "/todos/"+created.ID, bytes.NewReader(body))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec = httptest.NewRecorder()
	c = e.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(created.ID)

	err = handler.UpdateTodo(c)
	require.NoError(t, err)

	assert.Equal(t, http.StatusOK, rec.Code)

	var response models.Todo
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Equal(t, newTitle, response.Title)
	assert.True(t, response.Completed)
}

func TestTodoHandler_DeleteTodo(t *testing.T) {
	handler, e, cleanup := setupTestHandler(t)
	defer cleanup()

	// Create a todo first
	reqBody := models.CreateTodoRequest{
		Title:       "Test Todo",
		Description: "Test Description",
	}
	body, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPost, "/todos", bytes.NewReader(body))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	err := handler.CreateTodo(c)
	require.NoError(t, err)

	var created models.Todo
	err = json.Unmarshal(rec.Body.Bytes(), &created)
	require.NoError(t, err)

	// Delete the todo
	req = httptest.NewRequest(http.MethodDelete, "/todos/"+created.ID, nil)
	rec = httptest.NewRecorder()
	c = e.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(created.ID)

	err = handler.DeleteTodo(c)
	require.NoError(t, err)

	assert.Equal(t, http.StatusOK, rec.Code)

	var response map[string]string
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Contains(t, response, "message")
}

func TestTodoHandler_BatchDeleteTodos(t *testing.T) {
	handler, e, cleanup := setupTestHandler(t)
	defer cleanup()

	// Create multiple todos
	var createdTodos []models.Todo
	for i := 0; i < 3; i++ {
		reqBody := models.CreateTodoRequest{
			Title:       fmt.Sprintf("Test Todo %d", i+1),
			Description: fmt.Sprintf("Test Description %d", i+1),
		}
		body, _ := json.Marshal(reqBody)

		req := httptest.NewRequest(http.MethodPost, "/todos", bytes.NewReader(body))
		req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
		rec := httptest.NewRecorder()
		c := e.NewContext(req, rec)

		err := handler.CreateTodo(c)
		require.NoError(t, err)

		var created models.Todo
		err = json.Unmarshal(rec.Body.Bytes(), &created)
		require.NoError(t, err)
		createdTodos = append(createdTodos, created)
	}

	// Batch delete two todos
	batchReq := models.BatchDeleteTodosRequest{
		IDs: []string{createdTodos[0].ID, createdTodos[1].ID},
	}
	body, _ := json.Marshal(batchReq)

	req := httptest.NewRequest(http.MethodPost, "/todos/batch-delete", bytes.NewReader(body))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	err := handler.BatchDeleteTodos(c)
	require.NoError(t, err)

	assert.Equal(t, http.StatusOK, rec.Code)

	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)
	assert.Contains(t, response, "message")
	assert.Contains(t, response, "deleted_count")
	assert.Equal(t, float64(2), response["deleted_count"])
}

func TestTodoHandler_BatchDeleteTodos_InvalidJSON(t *testing.T) {
	handler, e, cleanup := setupTestHandler(t)
	defer cleanup()

	req := httptest.NewRequest(http.MethodPost, "/todos/batch-delete", bytes.NewReader([]byte("invalid json")))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	err := handler.BatchDeleteTodos(c)
	require.NoError(t, err)

	assert.Equal(t, http.StatusBadRequest, rec.Code)
}

func TestTodoHandler_BatchDeleteTodos_EmptyRequest(t *testing.T) {
	handler, e, cleanup := setupTestHandler(t)
	defer cleanup()

	batchReq := models.BatchDeleteTodosRequest{
		IDs: []string{},
	}
	body, _ := json.Marshal(batchReq)

	req := httptest.NewRequest(http.MethodPost, "/todos/batch-delete", bytes.NewReader(body))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	err := handler.BatchDeleteTodos(c)
	require.NoError(t, err)

	assert.Equal(t, http.StatusUnprocessableEntity, rec.Code) // Validation error
}

func TestTodoHandler_BatchDeleteTodos_InvalidIDs(t *testing.T) {
	handler, e, cleanup := setupTestHandler(t)
	defer cleanup()

	batchReq := models.BatchDeleteTodosRequest{
		IDs: []string{"invalid-uuid", "another-invalid"},
	}
	body, _ := json.Marshal(batchReq)

	req := httptest.NewRequest(http.MethodPost, "/todos/batch-delete", bytes.NewReader(body))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := e.NewContext(req, rec)

	err := handler.BatchDeleteTodos(c)
	require.NoError(t, err)

	assert.Equal(t, http.StatusBadRequest, rec.Code)
}
