# TODO API

A modern, RESTful TODO API built with Go and Echo framework, following clean architecture principles and Go best practices.

## Features

- ✅ RESTful API endpoints for CRUD operations
- ✅ Clean architecture with separation of concerns
- ✅ PostgreSQL database integration with connection pooling
- ✅ Database migrations system
- ✅ In-memory storage option for development/testing
- ✅ Input validation and error handling
- ✅ Comprehensive unit and integration tests
- ✅ Configuration management via environment variables
- ✅ Graceful shutdown
- ✅ Rate limiting and security middleware
- ✅ JSON serialization/deserialization
- ✅ UUID-based todo IDs
- ✅ Proper HTTP status codes

## Project Structure

```
backend/
├── cmd/
│   └── main.go                 # Application entry point
├── internal/
│   ├── config/                 # Configuration management
│   │   └── config.go
│   ├── database/               # Database connection management
│   │   └── postgres.go
│   ├── errors/                 # Custom error types
│   │   └── errors.go
│   ├── handlers/               # HTTP handlers
│   │   ├── todo.go
│   │   └── todo_test.go
│   ├── migrations/             # Database migrations
│   │   ├── migrate.go
│   │   ├── 001_create_todos_table.up.sql
│   │   └── 001_create_todos_table.down.sql
│   ├── models/                 # Data models
│   │   ├── todo.go
│   │   └── todo_test.go
│   ├── repository/             # Data access layer
│   │   ├── todo.go             # Repository interface
│   │   ├── postgres_todo.go    # PostgreSQL repository
│   │   └── postgres_todo_test.go
│   ├── routes/                 # Route configuration
│   │   └── routes.go
│   └── service/                # Business logic layer
│       ├── todo.go
│       └── todo_test.go
├── migrations/                 # SQL migration files
├── test/                       # Test utilities
│   └── testutil/
│       └── database.go
├── go.mod
├── go.sum
└── README.md
```

## API Endpoints

### Base URL
```
http://localhost:8080/api/v1
```

### Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/todos` | Get all todos |
| GET | `/todos/{id}` | Get a specific todo by ID |
| POST | `/todos` | Create a new todo |
| PUT | `/todos/{id}` | Update an existing todo |
| DELETE | `/todos/{id}` | Delete a todo |
| POST | `/todos/batch-delete` | Delete multiple todos atomically |
| GET | `/health` | Health check endpoint |

## Data Model

### Todo
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "title": "Learn Go",
  "description": "Study Go programming language",
  "completed": false,
  "created_at": "2023-12-01T10:00:00Z",
  "updated_at": "2023-12-01T10:00:00Z"
}
```

### Create Todo Request
```json
{
  "title": "Learn Go",
  "description": "Study Go programming language"
}
```

### Update Todo Request
```json
{
  "title": "Learn Go Advanced",
  "description": "Study advanced Go concepts",
  "completed": true
}
```

### Batch Delete Todos Request
```json
{
  "ids": [
    "550e8400-e29b-41d4-a716-************",
    "550e8400-e29b-41d4-a716-************",
    "550e8400-e29b-41d4-a716-************"
  ]
}
```

**Validation Rules:**
- `ids`: Required array of 1-100 valid UUID strings
- Duplicate IDs are automatically deduplicated
- Empty strings and whitespace are automatically cleaned

## Getting Started

### Prerequisites
- Go 1.21 or higher
- PostgreSQL 12 or higher
- Git

### Database Setup

1. **Create PostgreSQL Database**:
```sql
CREATE DATABASE todo_db;
CREATE USER todo_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE todo_db TO todo_user;
```

2. **Configure Environment Variables**:

Create a `.env` file in the backend directory:
```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=todo_db
DB_USER=todo_user
DB_PASSWORD=your_password
DB_SSLMODE=prefer
DB_MAX_CONNS=25
DB_MIN_CONNS=5
# DB_USE_POOLER=false  # Optional: Override auto-detection of connection pooler

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
```

Alternatively, you can set environment variables directly:
```bash
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=todo_db
export DB_USER=todo_user
export DB_PASSWORD=your_password
export DB_SSLMODE=prefer
```

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd backend
```

2. Install dependencies:
```bash
go mod tidy
```

3. Set up the database and configuration (see Database Setup above)

4. Run the application:
```bash
go run cmd/main.go
```

The server will start on `http://localhost:8080` and automatically run database migrations.

### Configuration

The application can be configured using a `.env` file or environment variables. The application will first try to load from a `.env` file in the backend directory, then fall back to system environment variables for backward compatibility.

#### Server Configuration
| Variable | Default | Description |
|----------|---------|-------------|
| `SERVER_HOST` | `0.0.0.0` | Server host address |
| `SERVER_PORT` | `8080` | Server port |

#### Database Configuration
| Variable | Default | Description |
|----------|---------|-------------|
| `DB_HOST` | `localhost` | PostgreSQL host |
| `DB_PORT` | `5432` | PostgreSQL port |
| `DB_NAME` | `todo_db` | Database name |
| `DB_USER` | `postgres` | Database username |
| `DB_PASSWORD` | `` | Database password |
| `DB_SSLMODE` | `prefer` | SSL mode (disable, require, prefer) |
| `DB_MAX_CONNS` | `25` | Maximum database connections |
| `DB_MIN_CONNS` | `5` | Minimum database connections |
| `DB_USE_POOLER` | `auto-detected` | Whether a connection pooler is being used (true/false). Auto-detected if hostname contains "pool" |

Example:
```bash
export SERVER_HOST=localhost
export SERVER_PORT=3000
go run cmd/main.go
```

## Database Schema

### Todos Table

```sql
CREATE TABLE todos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL CHECK (length(trim(title)) > 0),
    description TEXT DEFAULT '',
    completed BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_todos_completed ON todos(completed);
CREATE INDEX idx_todos_created_at ON todos(created_at);
CREATE INDEX idx_todos_updated_at ON todos(updated_at);
```

### Migrations

The application uses an embedded migration system that automatically runs when the application starts. Migration files are located in `internal/migrations/`:

- `001_create_todos_table.up.sql` - Creates the todos table
- `001_create_todos_table.down.sql` - Drops the todos table

To manually run migrations:
```bash
# The application automatically runs migrations on startup
# No manual intervention needed
```

## API Usage Examples

### Create a Todo
```bash
curl -X POST http://localhost:8080/api/v1/todos \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Learn Go",
    "description": "Study Go programming language"
  }'
```

### Get All Todos
```bash
curl http://localhost:8080/api/v1/todos
```

### Get a Specific Todo
```bash
curl http://localhost:8080/api/v1/todos/{todo-id}
```

### Update a Todo
```bash
curl -X PUT http://localhost:8080/api/v1/todos/{todo-id} \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Learn Go Advanced",
    "completed": true
  }'
```

### Delete a Todo
```bash
curl -X DELETE http://localhost:8080/api/v1/todos/{todo-id}
```

### Batch Delete Todos
```bash
curl -X POST http://localhost:8080/api/v1/todos/batch-delete \
  -H "Content-Type: application/json" \
  -d '{
    "ids": [
      "550e8400-e29b-41d4-a716-************",
      "550e8400-e29b-41d4-a716-************",
      "550e8400-e29b-41d4-a716-************"
    ]
  }'
```

**Response:**
```json
{
  "message": "todos deleted successfully",
  "deleted_count": 3
}
```

**Features:**
- **Atomic operation**: Either all specified todos are deleted, or none are deleted if an error occurs
- **Bulk processing**: Accepts 1-100 todo IDs per request
- **Smart handling**: Automatically handles duplicate IDs and whitespace
- **Validation**: Validates UUID format for all IDs
- **Flexible**: Non-existent IDs are silently ignored (no error thrown)
- **Informative**: Returns count of actually deleted todos

### Health Check
```bash
curl http://localhost:8080/api/v1/health
```

## Testing

Run all tests:
```bash
go test ./...
```

Run tests with coverage:
```bash
go test -cover ./...
```

Run tests with verbose output:
```bash
go test -v ./...
```

### PostgreSQL Integration Tests

All tests now use PostgreSQL as the storage backend. To run tests, set up a test database:

1. **Create test database**:
```sql
CREATE DATABASE todo_test;
GRANT ALL PRIVILEGES ON DATABASE todo_test TO todo_user;
```

2. **Set test environment variables**:
```bash
export DB_HOST=localhost
export DB_NAME=todo_test
export DB_USER=todo_user
export DB_PASSWORD=your_password
export DB_SSLMODE=prefer
```

3. **Run tests**:
```bash
go test ./internal/repository -v
```

The PostgreSQL tests will automatically:
- Connect to the test database
- Run migrations
- Execute tests with transaction isolation
- Clean up test data

## Building

Build the application:
```bash
go build -o todo-api cmd/main.go
```

Run the built binary:
```bash
./todo-api
```

## Error Handling

The API returns appropriate HTTP status codes and error messages:

- `200 OK` - Successful GET, PUT requests
- `201 Created` - Successful POST requests
- `400 Bad Request` - Invalid request body or parameters
- `404 Not Found` - Todo not found
- `422 Unprocessable Entity` - Validation errors
- `500 Internal Server Error` - Server errors

Error response format:
```json
{
  "error": true,
  "message": "Error description"
}
```

## Validation Rules

### Todo Title
- Required
- Minimum length: 1 character
- Maximum length: 200 characters

### Todo Description
- Optional
- Maximum length: 1000 characters

## Architecture

This application follows clean architecture principles:

1. **Models Layer**: Defines data structures and business entities
2. **Repository Layer**: Handles data persistence using PostgreSQL
3. **Service Layer**: Contains business logic and validation
4. **Handler Layer**: Manages HTTP requests and responses
5. **Routes Layer**: Configures API endpoints and middleware

## Dependencies

- [Echo](https://echo.labstack.com/) - High performance, minimalist Go web framework
- [pgx](https://github.com/jackc/pgx) - PostgreSQL driver and toolkit
- [godotenv](https://github.com/joho/godotenv) - Environment variable loading from .env files
- [UUID](https://github.com/google/uuid) - UUID generation
- [Validator](https://github.com/go-playground/validator) - Struct validation
- [Testify](https://github.com/stretchr/testify) - Testing toolkit

## Future Enhancements

- [x] PostgreSQL database integration
- [x] Database migrations system
- [x] Connection pooling
- [ ] Authentication and authorization
- [ ] Pagination for todo lists
- [ ] Filtering and sorting
- [ ] Todo categories/tags
- [ ] Due dates and reminders
- [ ] API documentation with Swagger
- [ ] Docker containerization
- [ ] Logging with structured logs
- [ ] Metrics and monitoring
- [ ] Database backup and recovery
- [ ] Read replicas support
- [ ] Caching layer (Redis)
- [ ] Full-text search
